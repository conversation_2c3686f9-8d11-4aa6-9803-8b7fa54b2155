// WebSocket接続を確立
const ws = new WebSocket("ws://localhost:8080/ws");

// 接続が確立された時のイベント
ws.onopen = () => {
    console.log("WebSocket接続が確立されました！");
    ws.send("Hello from <PERSON>rowser!"); // メッセージを送信
};

// メッセージを受信した時のイベント
ws.onmessage = (event) => {
    console.log("サーバーからメッセージを受信:", event.data);
};

// エラーが発生した時のイベント
ws.onerror = (error) => {
    console.error("WebSocketエラー:", error);
};

// 接続が閉じられた時のイベント
ws.onclose = () => {
    console.log("WebSocket接続が閉じられました。");
};

// 追加でメッセージを送信したい場合
setTimeout(() => {
    ws.send("Another message from Brows<PERSON>!");
}, 2000);

// 接続を閉じたい場合
// ws.close();