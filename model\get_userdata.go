package model

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

const userdb string = "userdata.sqlite3"

type user struct {
	userid   int
	username string
}

func GetUserid() {
	DbConnection, err := sql.Open("sqlite3", userdb)
	if err != nil {
		log.Fatalf("failed to open \"userdata.sqlite3\":%v", err)
	}
	defer DbConnection.Close()

	Username_from_id(DbConnection, 1)
}

func Username_from_id(DbConnection *sql.DB, id int) user {
	var u user
	query_select_from_id := "SELECT userid,username FROM user WHERE userid=?"
	row := DbConnection.QueryRow(query_select_from_id, id)

	err := row.Scan(&u.userid, &u.username)
	if err != nil {
		log.Printf("no data in databass :%v", err)
	}
	fmt.Println(u.userid, u.username)

	return u
}
